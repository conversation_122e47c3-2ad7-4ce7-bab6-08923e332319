#!/usr/bin/env python3
"""MCP PyAutoGUI Server - Desktop automation server for Model Context Protocol."""

import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional, Tuple, Union
import base64
import io

import pyautogui
from PIL import Image
from mcp import Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
)

# Configure PyAutoGUI
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.1

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create server instance
server = Server("mcp-pyautogui-server")


@server.list_tools()
async def handle_list_tools() -> ListToolsResult:
    """List available tools."""
    return ListToolsResult(
        tools=[
            Tool(
                name="mouse_move",
                description="Move mouse to specified coordinates",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "x": {"type": "integer", "description": "X coordinate"},
                        "y": {"type": "integer", "description": "Y coordinate"},
                        "duration": {"type": "number", "description": "Duration in seconds", "default": 0.0}
                    },
                    "required": ["x", "y"]
                }
            ),
            Tool(
                name="mouse_click",
                description="Click at current position or specified coordinates",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "x": {"type": "integer", "description": "X coordinate (optional)"},
                        "y": {"type": "integer", "description": "Y coordinate (optional)"},
                        "button": {"type": "string", "enum": ["left", "right", "middle"], "default": "left"},
                        "clicks": {"type": "integer", "description": "Number of clicks", "default": 1},
                        "interval": {"type": "number", "description": "Interval between clicks", "default": 0.0}
                    }
                }
            ),
            Tool(
                name="mouse_drag",
                description="Drag from current position to target coordinates",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "x": {"type": "integer", "description": "Target X coordinate"},
                        "y": {"type": "integer", "description": "Target Y coordinate"},
                        "duration": {"type": "number", "description": "Duration in seconds", "default": 1.0},
                        "button": {"type": "string", "enum": ["left", "right", "middle"], "default": "left"}
                    },
                    "required": ["x", "y"]
                }
            ),
            Tool(
                name="mouse_position",
                description="Get current mouse position",
                inputSchema={"type": "object", "properties": {}}
            ),
            Tool(
                name="type_text",
                description="Type text at current cursor position",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "text": {"type": "string", "description": "Text to type"},
                        "interval": {"type": "number", "description": "Interval between keystrokes", "default": 0.0}
                    },
                    "required": ["text"]
                }
            ),
            Tool(
                name="press_key",
                description="Press a key or key combination",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "key": {"type": "string", "description": "Key to press (e.g., 'enter', 'ctrl+c')"},
                        "presses": {"type": "integer", "description": "Number of times to press", "default": 1},
                        "interval": {"type": "number", "description": "Interval between presses", "default": 0.0}
                    },
                    "required": ["key"]
                }
            ),
            Tool(
                name="screenshot",
                description="Take a screenshot of the screen or a region",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "region": {
                            "type": "object",
                            "description": "Region to capture (left, top, width, height)",
                            "properties": {
                                "left": {"type": "integer"},
                                "top": {"type": "integer"},
                                "width": {"type": "integer"},
                                "height": {"type": "integer"}
                            }
                        }
                    }
                }
            ),
            Tool(
                name="find_image",
                description="Find an image on the screen",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "image_path": {"type": "string", "description": "Path to the image file to find"},
                        "confidence": {"type": "number", "description": "Confidence threshold (0.0-1.0)", "default": 0.9},
                        "region": {
                            "type": "object",
                            "description": "Region to search in (left, top, width, height)",
                            "properties": {
                                "left": {"type": "integer"},
                                "top": {"type": "integer"},
                                "width": {"type": "integer"},
                                "height": {"type": "integer"}
                            }
                        }
                    },
                    "required": ["image_path"]
                }
            ),
            Tool(
                name="get_screen_size",
                description="Get the screen size",
                inputSchema={"type": "object", "properties": {}}
            ),
            Tool(
                name="get_pixel_color",
                description="Get the color of a pixel at specified coordinates",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "x": {"type": "integer", "description": "X coordinate"},
                        "y": {"type": "integer", "description": "Y coordinate"}
                    },
                    "required": ["x", "y"]
                }
            )
        ]
    )


@server.call_tool()
async def handle_call_tool(request: CallToolRequest) -> CallToolResult:
    """Handle tool calls."""
    try:
        if request.name == "mouse_move":
            x = request.arguments["x"]
            y = request.arguments["y"]
            duration = request.arguments.get("duration", 0.0)
            pyautogui.moveTo(x, y, duration=duration)
            return CallToolResult(content=[TextContent(type="text", text=f"Mouse moved to ({x}, {y})")])
            
        elif request.name == "mouse_click":
            x = request.arguments.get("x")
            y = request.arguments.get("y")
            button = request.arguments.get("button", "left")
            clicks = request.arguments.get("clicks", 1)
            interval = request.arguments.get("interval", 0.0)
            
            if x is not None and y is not None:
                pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)
                return CallToolResult(content=[TextContent(type="text", text=f"Clicked at ({x}, {y}) with {button} button")])
            else:
                pyautogui.click(clicks=clicks, interval=interval, button=button)
                return CallToolResult(content=[TextContent(type="text", text=f"Clicked with {button} button")])
                
        elif request.name == "mouse_drag":
            x = request.arguments["x"]
            y = request.arguments["y"]
            duration = request.arguments.get("duration", 1.0)
            button = request.arguments.get("button", "left")
            pyautogui.drag(x, y, duration=duration, button=button)
            return CallToolResult(content=[TextContent(type="text", text=f"Dragged to ({x}, {y})")])
            
        elif request.name == "mouse_position":
            pos = pyautogui.position()
            return CallToolResult(content=[TextContent(type="text", text=f"Mouse position: ({pos.x}, {pos.y})")])
            
        elif request.name == "type_text":
            text = request.arguments["text"]
            interval = request.arguments.get("interval", 0.0)
            pyautogui.typewrite(text, interval=interval)
            return CallToolResult(content=[TextContent(type="text", text=f"Typed: {text}")])
            
        elif request.name == "press_key":
            key = request.arguments["key"]
            presses = request.arguments.get("presses", 1)
            interval = request.arguments.get("interval", 0.0)
            pyautogui.press(key, presses=presses, interval=interval)
            return CallToolResult(content=[TextContent(type="text", text=f"Pressed key: {key}")])
            
        elif request.name == "screenshot":
            region = request.arguments.get("region")
            if region:
                screenshot = pyautogui.screenshot(region=(region["left"], region["top"], region["width"], region["height"]))
            else:
                screenshot = pyautogui.screenshot()
            
            # Convert to base64
            buffer = io.BytesIO()
            screenshot.save(buffer, format='PNG')
            img_data = base64.b64encode(buffer.getvalue()).decode()
            
            return CallToolResult(content=[
                TextContent(type="text", text="Screenshot taken"),
                ImageContent(type="image", data=img_data, mimeType="image/png")
            ])
            
        elif request.name == "find_image":
            image_path = request.arguments["image_path"]
            confidence = request.arguments.get("confidence", 0.9)
            region = request.arguments.get("region")
            
            try:
                if region:
                    location = pyautogui.locateOnScreen(image_path, confidence=confidence, 
                                                     region=(region["left"], region["top"], region["width"], region["height"]))
                else:
                    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
                
                if location:
                    center = pyautogui.center(location)
                    return CallToolResult(content=[TextContent(type="text", 
                        text=f"Image found at: {location}, center: ({center.x}, {center.y})")])
                else:
                    return CallToolResult(content=[TextContent(type="text", text="Image not found")])
            except Exception as e:
                return CallToolResult(content=[TextContent(type="text", text=f"Error finding image: {str(e)}")])
                
        elif request.name == "get_screen_size":
            size = pyautogui.size()
            return CallToolResult(content=[TextContent(type="text", text=f"Screen size: {size.width}x{size.height}")])
            
        elif request.name == "get_pixel_color":
            x = request.arguments["x"]
            y = request.arguments["y"]
            color = pyautogui.pixel(x, y)
            return CallToolResult(content=[TextContent(type="text", text=f"Pixel color at ({x}, {y}): RGB{color}")])
            
        else:
            return CallToolResult(content=[TextContent(type="text", text=f"Unknown tool: {request.name}")], isError=True)
            
    except Exception as e:
        logger.error(f"Error in tool {request.name}: {str(e)}")
        return CallToolResult(content=[TextContent(type="text", text=f"Error: {str(e)}")], isError=True)


async def main():
    """Main entry point for the server."""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(read_stream, write_stream)


if __name__ == "__main__":
    asyncio.run(main())
