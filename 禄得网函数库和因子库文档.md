# 禄得网函数库和因子库文档

## 一、函数库

### 1. 截面函数 (Cross-Sectional Functions)

| 函数名 | 语法 | 功能描述 |
|--------|------|----------|
| cs_cor | cs_cor(指标1, 指标2) | 返回每交易日两个指标的当日协方差 |
| cs_corr | cs_corr(指标1, 指标2) | 返回每交易日两个指标的当日相关度 |
| cs_count | cs_count() | 统计交易日截面（不含停牌标的）的标的个数 |
| cs_mean | cs_mean(指标) | 计算交易日截面（不含停牌标的）指标的平均值 |
| cs_median | cs_median(指标) | 计算交易日截面（不含停牌标的）指标的中位数 |
| cs_min | cs_min(指标) | 计算交易日截面（不含停牌标的）指标的最小值 |
| cs_max | cs_max(指标) | 计算交易日截面（不含停牌标的）指标的最大值 |
| cs_pct_rank | cs_pct_rank(指标) | 计算交易日截面（不含停牌标的）指标的百分位排名 |
| cs_quantile | cs_quantile(指标, 分位数) | 计算交易日截面（不含停牌标的）指标的分位数 |
| cs_rank | cs_rank(指标, 排序标记) | 计算交易日截面（不含停牌标的）指标的排名。排序标记=False时，由小到大排名；排序标记=True时，由大到小排名 |
| cs_std | cs_std(指标) | 计算交易日截面（不含停牌标的）指标的标准差 |
| cs_sum | cs_sum(指标) | 计算交易日截面（不含停牌标的）指标的求和 |
| cs_var | cs_var(指标) | 计算交易日截面（不含停牌标的）指标的方差 |

### 2. 时序函数 (Time Series Functions)

| 函数名 | 语法 | 功能描述 |
|--------|------|----------|
| ts_arg_max | ts_arg_max(指标, 交易日) | 返回标的窗口内最大值的索引值 |
| ts_arg_min | ts_arg_min(指标, 交易日) | 返回标的窗口内最小值的索引值 |
| ts_cor | ts_cor(指标1, 指标2, 交易日) | 计算在过去几个交易日里两个指标的协方差 |
| ts_corr | ts_corr(指标1, 指标2, 交易日) | 计算在过去几个交易日里两个指标的相关性 |
| ts_ema | ts_ema(指标, 交易日) | EMA给予近期数据更大的权重，相比简单移动平均更能反映价格趋势 |
| ts_kurtosis | ts_kurtosis(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的峰度 |
| ts_shift | ts_shift(指标, 交易日) | 获取指标几个交易日前的值 |
| ts_max | ts_max(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的最大值 |
| ts_mean | ts_mean(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的平均值 |
| ts_median | ts_median(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的中位数 |
| ts_min | ts_min(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的最小值 |
| ts_quantile | ts_quantile(指标, 交易日, 分位数) | 计算指标在过去几个交易日（不含停牌日）排名百分点对应的值 |
| ts_skew | ts_skew(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的偏度 |
| ts_std | ts_std(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的标准差 |
| ts_sum | ts_sum(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的求和 |
| ts_var | ts_var(指标, 交易日) | 计算指标在过去几个交易日（不含停牌日）的方差 |

### 3. 数学函数 (Mathematical Functions)

| 函数名 | 语法 | 功能描述 |
|--------|------|----------|
| abs | abs(指标) | 计算指标的绝对值 |
| arccos | arccos(指标) | 计算指标的反余弦值 |
| arcsinh | arcsinh(指标) | 计算指标的反双曲正弦值 |
| arctan | arctan(指标) | 计算指标的反正切值 |
| arctanh | arctanh(指标) | 计算指标的反双曲正切值 |
| cbrt | cbrt(指标) | 计算指标的立方根 |
| ceil | ceil(指标) | 计算指标的向上取整 |
| cos | cos(指标) | 计算指标的余弦值 |
| cosh | cosh(指标) | 计算指标的双曲余弦值 |
| cot | cot(指标) | 计算指标的余切值 |
| degrees | degrees(指标) | 将指标转换为角度 |
| exp | exp(指标) | 计算指标的指数值 |
| floor | floor(指标) | 计算指标的向下取整 |
| log | log(指标) | 计算指标的自然对数 |
| radians | radians(指标) | 将指标转换为弧度 |
| round | round(指标, 小数位数) | 将指标取近似值 |
| sign | sign(指标) | 计算指标的正负符号 |
| sin | sin(指标) | 计算指标的正弦值 |
| sinh | sinh(指标) | 计算指标的双曲正弦值 |
| sqrt | sqrt(指标) | 计算指标的平方根 |
| tan | tan(指标) | 计算指标的正切值 |
| tanh | tanh(指标) | 计算指标的双曲正切值 |

### 4. 逻辑函数 (Logical Functions)

| 函数名 | 语法 | 功能描述 |
|--------|------|----------|
| and_ | and_(条件1, 条件2) | 逻辑与运算 |
| or_ | or_(条件1, 条件2) | 逻辑或运算 |
| not_ | not_(条件) | 逻辑非运算 |

### 5. 日历函数 (Calendar Functions)

| 函数名 | 语法 | 功能描述 |
|--------|------|----------|
| day_of_week | day_of_week(date_expr) | 计算指定日期表达式的星期几（1=周一，7=周日） |
| day_of_month | day_of_month(date_expr) | 计算指定日期表达式是当月的第几天（1-31） |
| day_of_year | day_of_year(date_expr) | 计算指定日期表达式是当年的第几天（1-366） |
| month_of_year | month_of_year(date_expr) | 计算指定日期表达式是当年的第几月（1-12） |
| year | year(date_expr) | 计算指定日期表达式的年份 |
| quarter | quarter(date_expr) | 计算指定日期表达式是当年的第几季度（1-4） |

### 6. 其他函数 (Other Functions)

| 函数名 | 语法 | 功能描述 |
|--------|------|----------|
| when | when(条件, 指标1, 指标2) | 根据条件返回不同的值（条件为真返回指标1，否则返回指标2） |
| contains | contains(字符串列, 子串) | 判断字符串列是否包含指定子串（支持正则表达式） |
| start_with | start_with(字符串列, 前缀) | 判断字符串列是否以指定前缀开头 |
| end_with | end_with(字符串列, 后缀) | 判断字符串列是否以指定后缀结尾 |

### 7. 技术指标 (Technical Indicators)

| 函数名 | 语法 | 功能描述 |
|--------|------|----------|
| MA多头 | MA多头(快线周期, 慢线周期, 指标) | MA快线在慢线上方 |
| MA空头 | MA空头(快线周期, 慢线周期, 指标) | MA快线在慢线下方 |
| EMA_多头 | EMA_多头(短期, 长期, 指标) | 当短期EMA在长期EMA上方时，返回1，否则返回0 |
| EMA_空头 | EMA_空头(短期, 长期, 指标) | 当短期EMA在长期EMA下方时，返回1，否则返回0 |
| 量比 | 量比(N, M) | 量比 = N日平均成交量 / M日平均成交量 |
| 乖离率 | 乖离率(周期, 指标) | 乖离率 = (指标值 - N日移动平均线) / N日移动平均线 |
| 布林中轨 | 布林中轨(周期, 指标) | 中轨 = N日移动平均线 |
| 布林上轨 | 布林上轨(周期, 标准差倍数, 指标) | 上轨 = 中轨 + 标准差倍数 * 标准差 |
| 布林下轨 | 布林下轨(周期, 标准差倍数, 指标) | 下轨 = 中轨 - 标准差倍数 * 标准差 |
| 布林带宽 | 布林带宽(周期, 标准差倍数, 指标) | 带宽 = (上轨 - 下轨) / 中轨 |
| 布林突破上轨 | 布林突破上轨(周期, 标准差倍数, 指标) | 当价格突破上轨时，返回1，否则返回0 |
| 布林突破下轨 | 布林突破下轨(周期, 标准差倍数, 指标) | 当价格突破下轨时，返回1，否则返回0 |
| 波动率 | 波动率(指标, 周期) | 波动率 = N日收益率的标准差 * sqrt(N) |

## 二、因子库（可转债基础因子）

| 因子名称 | 因子标识 | 功能描述 |
|----------|----------|----------|
| 期权价值 | [option_value] | 可转债的期权价值 |
| 理论偏离度 | [theory_bias] | 理论价值与市场价格的偏离程度 |
| 双低 | [dblow] | 转股溢价率和到期收益率的综合指标 |
| 换手率 | [turnover] | 成交量与流通股本的比率 |
| 纯债溢价率 | [bond_prem] | 相对于纯债价值的溢价率 |
| 剩余规模(亿) | [remain_size] | 剩余未转股的债券规模 |
| 收盘价 | [close] | 当日收盘价格 |
| 前收盘价 | [pre_close] | 前一交易日收盘价格 |
| 理论溢价率 | [theory_conv_prem] | 基于理论价值计算的转股溢价率 |
| 发行规模(亿) | [issue_size] | 可转债发行总规模 |
| 转股溢价率 | [conv_prem] | 转股价值相对于正股价值的溢价率 |
| 剩余市值(亿) | [remain_cap] | 剩余未转股部分的市值 |
| 修正溢价率 | [mod_conv_prem] | 修正后的转股溢价率 |
| 纯债价值 | [pure_value] | 作为纯债券的理论价值 |
| 最高价 | [high] | 当日最高价格 |
| 成交额(万) | [amount] | 当日成交金额 |
| 最低价 | [low] | 当日最低价格 |
| 转股价格 | [conv_price] | 转换为正股的价格 |
| 涨跌幅 | [pct_chg] | 相对于前一交易日的涨跌幅度 |
| 理论价值 | [theory_value] | 理论计算的可转债价值 |
| 转股价值 | [conv_value] | 转换为正股的价值 |
| 剩余年限 | [left_years] | 距离到期的剩余年限 |
| 成交量(手) | [vol] | 当日成交量 |
| 开盘价 | [open] | 当日开盘价格 |
| 转债市占比 | [cap_mv_rate] | 转债市值占正股市值的比例 |
| 到期收益率 | [ytm] | 持有到期的年化收益率 |
| 上市天数 | [list_days] | 自上市以来的交易天数 |
| 强赎剩余计数 | [redeem_remain_days] | 距离强制赎回的剩余天数 |
| 强赎触发价比率 | [redeem_price_rate] | 强制赎回触发价格比率 |
| 涨跌停标记 | [limit] | 是否触及涨跌停限制 |

## 三、可转债历史类因子库

| 因子名称 | 因子标识 | 功能描述 |
|----------|----------|----------|
| 5日乖离率 | [bias_5] | 5日价格相对于均线的偏离程度 |
| 5日涨跌幅 | [pct_chg_5] | 过去5个交易日的涨跌幅 |
| 年化波动率 | [volatility] | 年化价格波动率 |
| 5日成交量 | [vol_5] | 过去5个交易日平均成交量 |
| 5日超额涨跌幅 | [alpha_pct_chg_5] | 相对于基准的5日超额收益 |
| 5日换手率 | [turnover_5] | 过去5个交易日平均换手率 |
| 5日成交额 | [amount_5] | 过去5个交易日平均成交额 |
| 5日均价 | [close_ma_5] | 过去5个交易日平均收盘价 |
| 正股5日涨跌幅 | [pct_chg_5_stk] | 正股过去5个交易日的涨跌幅 |
| 正股年化波动率 | [volatility_stk] | 正股年化价格波动率 |

## 四、可转债正股相关因子库

| 因子名称 | 因子标识 | 功能描述 |
|----------|----------|----------|
| 正股涨跌幅 | [pct_chg_stk] | 正股当日涨跌幅 |
| 正股流通市值(亿) | [circ_mv] | 正股流通股份的市场价值 |
| 正股总市值(亿) | [total_mv] | 正股全部股份的市场价值 |
| 市盈率TTM | [pe_ttm] | 正股市值与过去12个月净利润的比率 |
| 正股成交额(万) | [amount_stk] | 正股当日成交金额 |
| 市销率TTM | [ps_ttm] | 正股市值与过去12个月营业收入的比率 |
| 正股成交量 | [vol_stk] | 正股当日成交股数 |
| 股息率 | [dv_ratio] | 正股股息与股价的比率 |
| 正股收盘价 | [close_stk] | 正股当日收盘价格 |
| 市净率 | [pb] | 正股市值与净资产的比率 |
| 资产负债率 | [debt_to_assets] | 正股总负债与总资产的比率 |

## 五、自定义因子示例

### 已有自定义因子：
- **一年内低点翻倍**: `when(前收盘价/ts_min(最低价,252)>=2, 1, 0)`
  - 功能：判断当前价格是否相对于一年内最低点翻倍

## 六、使用说明

### 创建自定义因子步骤：
1. 点击"+"按钮添加新因子
2. 填写因子名称和标识
3. 编写因子公式（使用函数库中的函数）
4. 设置精度和样式
5. 点击"调试"测试因子
6. 保存因子

### 注意事项：
- 因子标识会自动生成，也可以重新生成
- 支持颜色显示开关
- 可以设置小数精度
- 建议先调试再保存

## 七、股票基础因子库

| 因子名称 | 因子标识 | 功能描述 |
|----------|----------|----------|
| 涨跌停标记 | [limit] | 是否触及涨跌停限制 |
| 最低价 | [low] | 当日最低价格 |
| 前收盘价 | [pre_close] | 前一交易日收盘价格 |
| 最高价 | [high] | 当日最高价格 |
| 成交额 | [amount] | 当日成交金额 |
| 开盘价 | [open] | 当日开盘价格 |
| 换手率 | [turnover] | 成交量与流通股本的比率 |
| 涨跌幅 | [pct_chg] | 相对于前一交易日的涨跌幅度 |
| 收盘价 | [close] | 当日收盘价格 |
| 市销率(TTM) | [ps_ttm] | 市值与过去12个月营业收入的比率 |
| 流通市值 | [circ_mv] | 流通股份的市场价值 |
| 总市值 | [total_mv] | 全部股份的市场价值 |
| 市净率 | [pb] | 市值与净资产的比率 |
| 流通股本 | [float_share] | 可自由交易的股份数量 |
| 市盈率(TTM) | [pe_ttm] | 市值与过去12个月净利润的比率 |
| 总股本 | [total_share] | 公司发行的全部股份数量 |
| 成交量 | [vol] | 当日成交股数 |
| 营业总收入 | [toi] | 公司营业总收入 |
| 归母权益合计 | [tetoshopc] | 归属于母公司股东的权益合计 |
| 归母扣非净利润TTM | [npadnrpatoshaopc_ttm] | 归母扣除非经常性损益净利润TTM |
| 归母净利润TTM | [npatoshopc_ttm] | 归属于母公司股东净利润TTM |
| 扣除商誉归母权益合计 | [tetoshopc_wo_gw] | 扣除商誉后的归母权益合计 |
| 经营现金流量净额 | [ncffoa] | 经营活动产生的现金流量净额 |
| 股息TTM | [dy_ttm] | 过去12个月的股息 |
| ROE | [roe] | 净资产收益率 |
| ROA | [roa] | 总资产收益率 |
| 资产负债率 | [tl_ta_r] | 总负债与总资产的比率 |
| 有息负债率 | [lwi_ta_r] | 有息负债与总资产的比率 |
| 市盈率TTM倒数 | [ep_ttm] | 市盈率TTM的倒数（盈利收益率） |
| 扣非市盈率TTM | [d_pe_ttm] | 扣除非经常性损益的市盈率TTM |
| 扣非市盈率TTM倒数 | [d_ep_ttm] | 扣非市盈率TTM的倒数 |
| 市净率倒数 | [bp] | 市净率的倒数（账面市值比） |
| 扣除商誉市净率 | [pb_wo_gw] | 扣除商誉后的市净率 |
| 扣除商誉市净率倒数 | [bp_wo_gw] | 扣除商誉市净率的倒数 |
| 市销率 | [ps] | 市值与营业收入的比率 |
| 市现率 | [pcf] | 市值与现金流的比率 |
| 股息率 | [dyr] | 股息与股价的比率 |
| 复权因子 | [adj_factor] | 用于价格复权计算的因子 |
| 市现率TTM | [pcf_ttm] | 市值与过去12个月现金流的比率 |
| 经营现金流量净额TTM | [ncffoa_ttm] | 过去12个月经营现金流量净额 |
| 营业总收入TTM | [toi_ttm] | 过去12个月营业总收入 |
| 上市自然日天数 | [list_days] | 自上市以来的自然日天数 |
| 上市交易日天数 | [list_trade_days] | 自上市以来的交易日天数 |

## 八、已有自定义因子（股票）

| 因子名称 | 因子公式 | 功能描述 |
|----------|----------|----------|
| 一年内低点翻倍 | when(前收盘价/ts_min(最低价,252)>=1.2, 1, 0) | 判断当前价格是否相对于一年内最低点上涨20%以上 |
| 一年内涨幅 | 前收盘价/ts_min(最低价,252) | 计算相对于一年内最低点的涨幅倍数 |

*注：可转债相关的因子库在可转债标签页*

## 九、重要说明：禄得网因子标识写法

### ⚠️ **关键提醒：禄得网使用中文因子标识，不是英文！**

**错误写法**: `[total_mv]`, `[pe_ttm]`, `[roe]`
**正确写法**: `总市值`, `市盈率TTM`, `ROE`

### 📋 **常用因子的正确中文标识对照表**

| 英文标识 | 正确的中文标识 | 说明 |
|----------|---------------|------|
| [total_mv] | 总市值 | 全部股份的市场价值 |
| [circ_mv] | 流通市值 | 流通股份的市场价值 |
| [pe_ttm] | 市盈率TTM | 市值与过去12个月净利润的比率 |
| [ep_ttm] | 市盈率TTM倒数 | 盈利收益率 |
| [pb] | 市净率 | 市值与净资产的比率 |
| [ps_ttm] | 市销率TTM | 市值与过去12个月营业收入的比率 |
| [roe] | ROE | 净资产收益率 |
| [roa] | ROA | 总资产收益率 |
| [tl_ta_r] | 资产负债率 | 总负债与总资产的比率 |
| [amount] | 成交额 | 当日成交金额 |
| [turnover] | 换手率 | 成交量与流通股本的比率 |
| [close] | 收盘价 | 当日收盘价格 |
| [dyr] | 股息率 | 股息与股价的比率 |

## 十、甘泉神奇公式策略因子（正确版本）

基于甘泉神奇公式策略的复现需求，以下是正确的自定义因子：

### 1. 基础筛选因子

#### 市值筛选因子
- **因子名称**: 市值筛选
- **因子公式**: `when(and_(总市值 >= 50, 总市值 <= 5000), 1, 0)`
- **功能**: 筛选50-5000亿市值的中等规模股票

#### 流动性筛选因子
- **因子名称**: 流动性充足
- **因子公式**: `when(成交额 > 50000, 1, 0)`
- **功能**: 筛选日成交额大于5000万的股票

#### 财务健康因子
- **因子名称**: 财务健康
- **因子公式**: `when(资产负债率 < 0.7, 1, 0)`
- **功能**: 筛选资产负债率小于70%的公司

#### 盈利能力因子
- **因子名称**: 盈利能力
- **因子公式**: `when(ROE > 0.05, 1, 0)`
- **功能**: 筛选ROE大于5%的优质公司

### 2. 神奇公式核心因子

#### 盈利收益率排名因子
- **因子名称**: 盈利收益率排名
- **因子公式**: `cs_rank(市盈率TTM倒数, True)`
- **功能**: 盈利收益率从高到低排名

#### 资本回报率排名因子
- **因子名称**: 资本回报率排名
- **因子公式**: `cs_rank(ROE, True)`
- **功能**: ROE从高到低排名

#### 神奇公式评分因子
- **因子名称**: 神奇公式评分
- **因子公式**: `盈利收益率排名 + 资本回报率排名`
- **功能**: 神奇公式综合评分（越小越好）

#### 神奇公式排名因子
- **因子名称**: 神奇公式排名
- **因子公式**: `cs_rank(神奇公式评分, False)`
- **功能**: 神奇公式最终排名

### 3. 风险控制因子

#### 估值合理因子
- **因子名称**: 估值合理
- **因子公式**: `when(and_(市盈率TTM > 0, 市盈率TTM < 50), 1, 0)`
- **功能**: 筛选PE在0-50倍之间的合理估值股票

#### PB合理因子
- **因子名称**: PB合理估值
- **因子公式**: `when(and_(市净率 > 0, 市净率 < 10), 1, 0)`
- **功能**: 筛选PB在0-10倍之间的合理估值股票

#### 波动率控制因子
- **因子名称**: 波动率筛选
- **因子公式**: `when(波动率(收盘价, 60) < 0.4, 1, 0)`
- **功能**: 筛选年化波动率小于40%的股票

#### 分红偏好因子
- **因子名称**: 分红偏好
- **因子公式**: `when(股息率 > 0, 1, 0)`
- **功能**: 偏好有分红记录的股票

### 4. 技术分析因子（可选）

#### 趋势确认因子
- **因子名称**: 多头趋势
- **因子公式**: `EMA_多头(20, 60, 收盘价)`
- **功能**: 确认股票处于多头趋势

#### 超跌反弹因子
- **因子名称**: 反弹信号
- **因子公式**: `when(收盘价 > 布林下轨(20, 2, 收盘价), 1, 0)`
- **功能**: 识别从超跌状态反弹的股票

### 5. 综合选股因子

#### 基础筛选综合因子
- **因子名称**: 基础筛选综合
- **因子公式**: `when(市值筛选 * 流动性充足 * 财务健康 * 盈利能力 == 1, 1, 0)`
- **功能**: 综合基础筛选条件

#### 风险控制综合因子
- **因子名称**: 风险控制综合
- **因子公式**: `波动率筛选 * 估值合理 * PB合理估值`
- **功能**: 综合风险控制条件

#### 最终评分因子
- **因子名称**: 最终评分
- **因子公式**: `when(and_(基础筛选综合 == 1, 风险控制综合 >= 2), 神奇公式排名, 9999)`
- **功能**: 通过筛选的股票按神奇公式排名

#### 选股信号因子
- **因子名称**: 选股信号
- **因子公式**: `when(cs_rank(最终评分, False) <= 20, 1, 0)`
- **功能**: 选择最终评分最小的20只股票

## 十一、甘泉神奇公式策略完整因子创建清单

### 📋 **按顺序创建的因子列表（共20个）**

#### 第一批：基础筛选因子（1-5）
1. **市值筛选**: `when(and_(总市值 >= 50, 总市值 <= 5000), 1, 0)`
2. **流动性充足**: `when(成交额 > 50000, 1, 0)`
3. **财务健康**: `when(资产负债率 < 0.7, 1, 0)`
4. **盈利能力**: `when(ROE > 0.05, 1, 0)`
5. **基础筛选综合**: `when(市值筛选 * 流动性充足 * 财务健康 * 盈利能力 == 1, 1, 0)`

#### 第二批：神奇公式核心因子（6-9）
6. **盈利收益率排名**: `cs_rank(市盈率TTM倒数, True)`
7. **资本回报率排名**: `cs_rank(ROE, True)`
8. **神奇公式评分**: `盈利收益率排名 + 资本回报率排名`
9. **神奇公式排名**: `cs_rank(神奇公式评分, False)`

#### 第三批：风险控制因子（10-13）
10. **波动率筛选**: `when(波动率(收盘价, 60) < 0.4, 1, 0)`
11. **估值合理**: `when(and_(市盈率TTM > 0, 市盈率TTM < 50), 1, 0)`
12. **分红偏好**: `when(股息率 > 0, 1, 0)`
13. **PB合理估值**: `when(and_(市净率 > 0, 市净率 < 10), 1, 0)`

#### 第四批：最终选股因子（14-17）
14. **风险控制综合**: `波动率筛选 * 估值合理 * PB合理估值`
15. **最终评分**: `when(and_(基础筛选综合 == 1, 风险控制综合 >= 2), 神奇公式排名, 9999)`
16. **选股信号**: `when(cs_rank(最终评分, False) <= 20, 1, 0)`
17. **持仓权重**: `when(选股信号 == 1, 0.05, 0)`

#### 第五批：技术分析因子（可选，18-20）
18. **多头趋势**: `EMA_多头(20, 60, 收盘价)`
19. **反弹信号**: `when(收盘价 > 布林下轨(20, 2, 收盘价), 1, 0)`
20. **价格强势**: `when(收盘价 > ts_mean(收盘价, 20), 1, 0)`

### 🚀 **创建步骤指南**

#### 每个因子的创建步骤：
1. 点击左侧"+"按钮添加新因子
2. 输入因子名称（如：市值筛选）
3. 因子标识可以自动生成或手动输入
4. 在因子公式框中输入对应公式
5. 在备注说明中写明功能描述
6. 点击"调试"按钮测试因子
7. 确认无误后点击"保存"

#### 重要注意事项：
- **必须按顺序创建**：后面的因子会引用前面的因子
- **先调试再保存**：确保每个因子语法正确
- **使用中文标识**：总市值、ROE、收盘价等
- **可以分批创建**：建议先创建前两批进行测试

### ⚠️ **常见错误提醒**

1. **因子标识错误**：
   - ❌ 错误：`[total_mv]`
   - ✅ 正确：`总市值`

2. **函数语法错误**：
   - ❌ 错误：`when(总市值 > 50 and 总市值 < 5000, 1, 0)`
   - ✅ 正确：`when(and_(总市值 >= 50, 总市值 <= 5000), 1, 0)`

3. **因子引用错误**：
   - ❌ 错误：在创建"基础筛选综合"前就引用它
   - ✅ 正确：按顺序创建，确保被引用的因子已存在

## 十二、使用建议

### 策略应用：
1. 在多因子轮动策略中添加这些自定义因子
2. 设置合适的权重和阈值
3. 结合现有的技术指标进行综合评分
4. 定期回测和调整参数

### 注意事项：
- 建议先用小资金测试新因子的效果
- 定期检查因子的有效性
- 根据市场环境调整因子权重
- 避免过度拟合历史数据
