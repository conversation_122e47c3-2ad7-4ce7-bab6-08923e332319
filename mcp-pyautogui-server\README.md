# mcp-pyautogui-server

A MCP (Model Context Protocol) server that provides automated GUI testing and control capabilities through PyAutoGUI.

## Features

- Control mouse movements and clicks
- Simulate keyboard input
- Take screenshots
- Find images on screen
- Get screen information
- Cross-platform support (Windows, macOS, Linux)

## Tools

The server implements the following tools:

### Mouse Control
- Move mouse to specific coordinates
- Click at current or specified position
- Drag and drop operations
- Get current mouse position

### Keyboard Control
- Type text
- Press individual keys
- Hotkey combinations

### Screen Operations
- Take screenshots
- Get screen size
- Find image locations on screen
- Get pixel colors

## Installation

### Prerequisites
- Python 3.12+
- PyAutoGUI
- Other dependencies will be installed automatically

### Install Steps

Install the package:
```bash
pip install -e .
```

### Claude Desktop Configuration

On MacOS:
```
~/Library/Application\ Support/Claude/claude_desktop_config.json
```

On Windows:
```
%APPDATA%/Claude/claude_desktop_config.json
```

Development/Unpublished Servers Configuration:
```json
{
  "mcpServers": {
    "mcp-pyautogui-server": {
      "command": "python",
      "args": [
        "-m", "mcp_pyautogui_server.server"
      ],
      "cwd": "/path/to/mcp-pyautogui-server"
    }
  }
}
```

## Development

### Building and Publishing

1. Sync dependencies and update lockfile:
```bash
pip install -e .
```

2. Test the server:
```bash
python -m mcp_pyautogui_server.server
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
