[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "mcp-pyautogui-server"
version = "0.1.0"
description = "A MCP (Model Context Protocol) server that provides automated GUI testing and control capabilities through PyAutoGUI."
readme = "README.md"
license = "MIT"
requires-python = ">=3.12"
authors = [
    { name = "hetaoBackend", email = "<EMAIL>" },
]
keywords = ["mcp", "pyautogui", "automation", "gui", "testing"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "mcp>=1.0.0",
    "pyautogui>=0.9.54",
    "pillow>=10.0.0",
    "opencv-python>=4.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
]

[project.scripts]
mcp-pyautogui-server = "mcp_pyautogui_server.server:main"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_pyautogui_server"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/README.md",
    "/LICENSE",
]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
strict = true
warn_return_any = true
warn_unused_configs = true
