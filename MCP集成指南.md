# MCP桌面自动化服务器集成指南

## 🎉 安装完成！

我已经成功为您创建并安装了MCP桌面自动化服务器。现在您可以通过Augment控制您的电脑软件了！

## 📁 文件结构

```
量化聊天/
├── mcp-pyautogui-server/          # MCP服务器代码
│   ├── src/mcp_pyautogui_server/  # 源代码
│   ├── pyproject.toml             # 项目配置
│   ├── README.md                  # 说明文档
│   └── LICENSE                    # 许可证
├── claude_desktop_config.json     # Augment配置文件
└── MCP集成指南.md                 # 本文件
```

## 🔧 配置Augment

### 方法1：自动配置（推荐）
将 `claude_desktop_config.json` 文件复制到以下位置：

**Windows:**
```
%APPDATA%\Claude\claude_desktop_config.json
```

**具体路径:**
```
C:\Users\<USER>\AppData\Roaming\Claude\claude_desktop_config.json
```

### 方法2：手动配置
如果您已有配置文件，请在现有配置中添加以下内容：

```json
{
  "mcpServers": {
    "mcp-pyautogui-server": {
      "command": "python",
      "args": [
        "-m", "mcp_pyautogui_server.server"
      ],
      "cwd": "c:\\Users\\<USER>\\Desktop\\量化聊天\\mcp-pyautogui-server"
    }
  }
}
```

## 🚀 可用功能

配置完成后，您可以通过我来控制电脑，包括：

### 🖱️ 鼠标控制
- 移动鼠标到指定坐标
- 在指定位置点击
- 拖拽操作
- 获取鼠标当前位置

### ⌨️ 键盘控制
- 输入文本
- 按键操作
- 快捷键组合

### 📸 屏幕操作
- 截屏
- 查找屏幕上的图像
- 获取屏幕尺寸
- 获取像素颜色

## 💡 使用示例

配置完成后，您可以这样使用：

1. **搜索微信中的"饕餮海投资"**：
   - "帮我在微信搜索框中输入'饕餮海投资'"
   - "点击微信搜索按钮"

2. **自动化操作**：
   - "截个屏看看当前界面"
   - "点击屏幕上的确定按钮"
   - "输入一段文字"

## ⚠️ 重要提示

1. **重启Augment**: 配置完成后需要重启Augment应用程序
2. **权限**: 确保Python有足够权限控制鼠标和键盘
3. **安全**: 这个工具可以控制您的电脑，请谨慎使用

## 🔍 测试配置

重启Augment后，您可以说：
- "截个屏"
- "获取鼠标位置"
- "获取屏幕尺寸"

如果这些命令有效，说明配置成功！

## 🆘 故障排除

如果遇到问题：

1. **检查Python路径**: 确保命令行中 `python` 命令可用
2. **检查工作目录**: 确保路径正确
3. **查看日志**: 重启Augment时查看是否有错误信息
4. **权限问题**: 以管理员身份运行Augment

## 📞 获取帮助

配置完成后，直接告诉我您想要执行的操作，我会帮您控制电脑！

例如：
- "帮我搜索微信中的饕餮海投资"
- "点击屏幕上的某个按钮"
- "自动填写表单"

---

🎊 **恭喜！您现在拥有了AI控制电脑的超能力！**
