#!/usr/bin/env python3
"""测试MCP PyAutoGUI服务器的基本功能"""

import pyautogui
import time

def test_basic_functions():
    """测试基本功能"""
    print("🧪 测试MCP PyAutoGUI服务器基本功能...")
    
    try:
        # 测试获取屏幕尺寸
        screen_size = pyautogui.size()
        print(f"✅ 屏幕尺寸: {screen_size.width}x{screen_size.height}")
        
        # 测试获取鼠标位置
        mouse_pos = pyautogui.position()
        print(f"✅ 鼠标位置: ({mouse_pos.x}, {mouse_pos.y})")
        
        # 测试截图功能
        print("📸 正在截图...")
        screenshot = pyautogui.screenshot()
        print(f"✅ 截图成功，尺寸: {screenshot.size}")
        
        # 测试鼠标移动（安全移动）
        print("🖱️ 测试鼠标移动...")
        original_pos = pyautogui.position()
        pyautogui.moveTo(original_pos.x + 10, original_pos.y + 10, duration=0.5)
        time.sleep(0.5)
        pyautogui.moveTo(original_pos.x, original_pos.y, duration=0.5)
        print("✅ 鼠标移动测试完成")
        
        print("\n🎉 所有基本功能测试通过！")
        print("📋 MCP服务器已准备就绪，可以集成到Augment中使用。")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False
    
    return True

def print_available_tools():
    """打印可用的工具列表"""
    print("\n🛠️ 可用的MCP工具:")
    tools = [
        "mouse_move - 移动鼠标到指定坐标",
        "mouse_click - 在指定位置点击",
        "mouse_drag - 拖拽操作",
        "mouse_position - 获取鼠标位置",
        "type_text - 输入文本",
        "press_key - 按键操作",
        "screenshot - 截屏",
        "find_image - 查找图像",
        "get_screen_size - 获取屏幕尺寸",
        "get_pixel_color - 获取像素颜色"
    ]
    
    for i, tool in enumerate(tools, 1):
        print(f"  {i:2d}. {tool}")

if __name__ == "__main__":
    print("🚀 MCP PyAutoGUI服务器功能测试")
    print("=" * 50)
    
    # 测试基本功能
    if test_basic_functions():
        print_available_tools()
        
        print("\n📝 下一步操作:")
        print("1. 将 claude_desktop_config.json 复制到 %APPDATA%\\Claude\\")
        print("2. 重启Augment应用程序")
        print("3. 告诉Augment: '帮我在微信中搜索饕餮海投资'")
        
    print("\n✨ 测试完成！")
